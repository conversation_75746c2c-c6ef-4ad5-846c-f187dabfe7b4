/*
 * Intel ACPI Component Architecture
 * AML/ASL+ Disassembler version 20250404 (32-bit version)
 * Copyright (c) 2000 - 2025 Intel Corporation
 * 
 * Disassembling to symbolic ASL+ operators
 *
 * Disassembly of SSDT-IVRS-Accurate.aml
 *
 * Original Table Header:
 *     Signature        "SSDT"
 *     Length           0x00000165 (357)
 *     Revision         0x02
 *     Checksum         0x59
 *     OEM ID           "AMD "
 *     OEM Table ID     "AmdTable"
 *     OEM Revision     0x00000001 (1)
 *     Compiler ID      "INTL"
 *     Compiler Version 0x20250404 (539296772)
 */
DefinitionBlock ("", "SSDT", 2, "AMD ", "AmdTable", 0x00000001)
{
    External (_SB_, DeviceObj)
    External (_SB_.PCI0, DeviceObj)

    Scope (_SB)
    {
        Device (IOV0)
        {
            Name (_HID, "AMDI0020")  // _HID: Hardware ID
            Name (_CID, "PNP0A08" /* PCI Express Bus */)  // _CID: Compatible ID
            Name (_UID, Zero)  // _UID: Unique ID
            Method (_STA, 0, NotSerialized)  // _STA: Status
            {
                Return (0x0F)
            }

            Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
            {
                Memory32Fixed (ReadWrite,
                    0xFD300000,         // Address Base
                    0x00040000,         // Address Length
                    )
            })
            Name (IVH0, Package (0x08)
            {
                0x10, 
                0xB0, 
                0x48, 
                0x02, 
                0x40, 
                0xFD300000, 
                Zero, 
                Zero
            })
        }

        Device (IOV1)
        {
            Name (_HID, "AMDI0020")  // _HID: Hardware ID
            Name (_UID, One)  // _UID: Unique ID
            Method (_STA, 0, NotSerialized)  // _STA: Status
            {
                Return (0x0F)
            }

            Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
            {
                Memory32Fixed (ReadWrite,
                    0xFD300000,         // Address Base
                    0x00040000,         // Address Length
                    )
            })
            Name (IVH1, Package (0x08)
            {
                0x11, 
                0xB0, 
                0x58, 
                0x02, 
                0x40, 
                0xFD300000, 
                Zero, 
                Zero
            })
        }
    }

    Scope (_SB.PCI0)
    {
        Device (IOMM)
        {
            Name (_ADR, 0x02)  // _ADR: Address
            Method (_STA, 0, NotSerialized)  // _STA: Status
            {
                Return (0x0F)
            }

            Name (PCFG, Package (0x10)
            {
                0x00021022, 
                0x00100006, 
                0x08060000, 
                0x00800000, 
                0xFD300000, 
                Zero, 
                Zero, 
                Zero, 
                Zero, 
                Zero, 
                Zero, 
                Zero, 
                Zero, 
                0x40, 
                Zero, 
                0x010B
            })
            Name (CAPS, Package (0x04)
            {
                One, 
                Zero, 
                0xB000, 
                0xFD300000
            })
        }
    }
}

