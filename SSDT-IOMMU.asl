/*
 * SSDT-IOMMU.asl
 * AMD IOMMU设备注入，符合ACPI 6.0规范
 * 用于模拟IOMMU功能，使DMA保护显示为开启状态
 */
DefinitionBlock ("", "SSDT", 2, "HACK", "IOMMU", 0x00001000)
{
    External (_SB_, DeviceObj)
    
    Scope (_SB)
    {
        Device (IVRS)
        {
            Name (_HID, "AMDI0020")
            Name (_UID, Zero)
            
            Method (_STA, 0, NotSerialized)
            {
                Return (0x0F)
            }
            
            Name (_CRS, ResourceTemplate ()
            {
                Memory32Fixed (ReadWrite, 0xFED80000, 0x00080000)
            })
        }
    }
}