/*
 * Intel ACPI Component Architecture
 * AML/ASL+ Disassembler version 20250404 (32-bit version)
 * Copyright (c) 2000 - 2025 Intel Corporation
 * 
 * Disassembling to symbolic ASL+ operators
 *
 * Disassembly of SSDT-IVRS-Real.aml
 *
 * Original Table Header:
 *     Signature        "SSDT"
 *     Length           0x00000154 (340)
 *     Revision         0x02
 *     Checksum         0x1A
 *     OEM ID           "AMD "
 *     OEM Table ID     "AmdTable"
 *     OEM Revision     0x00000001 (1)
 *     Compiler ID      "INTL"
 *     Compiler Version 0x20250404 (539296772)
 */
DefinitionBlock ("", "SSDT", 2, "AMD ", "AmdTable", 0x00000001)
{
    External (_SB_, DeviceObj)
    External (_SB_.PCI0, DeviceObj)

    Scope (_SB)
    {
        Device (AMD0)
        {
            Name (_HID, "AMDI0020")  // _HID: Hardware ID
            Name (_CID, "PNP0A08" /* PCI Express Bus */)  // _CID: Compatible ID
            Name (_UID, Zero)  // _UID: Unique ID
            Method (_STA, 0, NotSerialized)  // _STA: Status
            {
                Return (0x0F)
            }

            Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
            {
                Memory32Fixed (ReadWrite,
                    0xFD300000,         // Address Base
                    0x00040000,         // Address Length
                    )
                Memory32Fixed (ReadWrite,
                    0xFED80000,         // Address Base
                    0x00080000,         // Address Length
                    )
            })
            Name (IVHD, Package (0x04)
            {
                0x48B01011, 
                0x4000, 
                0xFD300000, 
                0x15D1
            })
        }

        Device (AMD1)
        {
            Name (_HID, "AMDI0020")  // _HID: Hardware ID
            Name (_UID, One)  // _UID: Unique ID
            Method (_STA, 0, NotSerialized)  // _STA: Status
            {
                Return (0x0F)
            }

            Name (_CRS, ResourceTemplate ()  // _CRS: Current Resource Settings
            {
                Memory32Fixed (ReadWrite,
                    0xFD320000,         // Address Base
                    0x00040000,         // Address Length
                    )
            })
            Name (IVHD, Package (0x04)
            {
                0x58B01011, 
                0x4000, 
                0xFD320000, 
                0x15D1
            })
        }
    }

    Scope (_SB.PCI0)
    {
        Device (IOMM)
        {
            Name (_ADR, 0x00010001)  // _ADR: Address
            Method (_STA, 0, NotSerialized)  // _STA: Status
            {
                Return (0x0F)
            }

            Name (PCFG, Package (0x08)
            {
                0x15D11022, 
                0x00100006, 
                0x08060001, 
                Zero, 
                0xFD300000, 
                Zero, 
                Zero, 
                0x10221022
            })
        }
    }
}

