/*
 * SSDT-IVRS-Real.asl
 * 基于真实IVRS.rw数据的精确仿真
 * 从IVRS表中提取的真实AMD IOMMU配置
 */
DefinitionBlock ("", "SSDT", 2, "AMD ", "AmdTable", 0x00000001)
{
    External (_SB_, DeviceObj)
    External (_SB.PCI0, DeviceObj)
    
    Scope (_SB)
    {
        // 主AMD IOMMU设备 - 基于真实IVRS数据
        Device (AMD0)
        {
            Name (_HID, "AMDI0020")
            Name (_CID, "PNP0A08")
            Name (_UID, Zero)
            
            Method (_STA, 0, NotSerialized)
            {
                Return (0x0F)
            }
            
            // 真实IOMMU寄存器地址范围
            Name (_CRS, ResourceTemplate ()
            {
                Memory32Fixed (ReadWrite, 0xFD300000, 0x00040000)
                Memory32Fixed (ReadWrite, 0xFED80000, 0x00080000)
            })
            
            // IVRS表特有的设备属性
            Name (IVHD, Package (0x04)
            {
                0x48B01011,    // IVHD Type 0x11 Header
                0x00004000,    // Flags from real IVRS
                0xFD300000,    // Real IOMMU base address
                0x15D1         // Real device ID
            })
        }
        
        // 第二个IOMMU单元（从IVRS数据中提取）
        Device (AMD1)
        {
            Name (_HID, "AMDI0020")
            Name (_UID, One)
            
            Method (_STA, 0, NotSerialized)
            {
                Return (0x0F)
            }
            
            Name (_CRS, ResourceTemplate ()
            {
                Memory32Fixed (ReadWrite, 0xFD320000, 0x00040000)
            })
            
            Name (IVHD, Package (0x04)
            {
                0x58B01011,    // Second IVHD from IVRS
                0x00004000,    // Matching flags
                0xFD320000,    // Second IOMMU address
                0x15D1         // Same device ID
            })
        }
    }
    
    // PCI0下的IOMMU控制器设备
    Scope (_SB.PCI0)
    {
        Device (IOMM)
        {
            Name (_ADR, 0x00010001)  // PCI地址Bus0 Dev1 Func1
            
            Method (_STA, 0, NotSerialized)
            {
                Return (0x0F)
            }
            
            // 模拟真实IOMMU PCI配置空间
            Name (PCFG, Package (0x08)
            {
                0x15D11022,    // Vendor:Device ID (AMD:IOMMU)
                0x00100006,    // Status:Command  
                0x08060001,    // Class:Revision
                0x00000000,    // BIST:Header:Latency:CacheLineSize
                0xFD300000,    // BAR0 - IOMMU registers
                0x00000000,    // BAR1
                0x00000000,    // BAR2  
                0x10221022     // Subsystem ID:Vendor
            })
        }
    }
}